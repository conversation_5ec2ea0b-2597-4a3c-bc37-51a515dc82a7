import { makeAutoObservable } from "mobx";
import { MappingState } from "../store/standard/MappingState";
import { StorageState } from "../store/standard/StorageState";
import { TransactionResponse } from "@ethersproject/providers";

// 空实现的 TokenState 类
export class TokenState {
  network?: any;
  balance?: any;

  constructor(args: Partial<TokenState> = {}) {
    Object.assign(this, args);
    makeAutoObservable(this);
  }
}

// CallParams 类型定义
export interface CallParams {
  address: string;
  abi: any;
  method: string;
  params?: any[];
  options?: any;
  read?: boolean;
  handler?: any;
}

// 其他缺失的类型定义
export interface CCSwapTokensPairs {
  [key: string]: any;
}

export interface CrossChain {
  tokens?: TokenState[];

  [key: string]: any;
}

export abstract class Network {
  // 从 ChainState 继承的属性
  name: string = "";
  fullName: string = "";
  alias?: string;
  logoUrl: string = "";
  isPaused: boolean = false;
  chainId: number = 0;
  rpcUrl: string = "";
  explorerName: string = "";
  explorerURL: string = "";
  Coin: TokenState = new TokenState();
  label?: string = "";
  confirmationTimes: number = 12;
  nativeCurrency: TokenState = new TokenState();
  info: {
    blockPerSeconds: number;
    multicallAddr?: string;
  } = { blockPerSeconds: 15 };
  ccSwapRouter?: string;
  clusterApiUrl?: string[];
  ccSwapTokensPairs: Partial<CCSwapTokensPairs> = {};
  crossChain: {
    [key: number]: Partial<CrossChain>;
  } = {};

  // 从 NetworkState 继承的属性

  constructor(config: {
    name: string;
    fullName: string;
    chainId: number;
    logoUrl: string;
    rpcUrl: string;
    explorerURL: string;
    explorerName: string;
    alias?: string;
    isPaused?: boolean;
    Coin?: TokenState;
    label?: string;
    confirmationTimes?: number;
    nativeCurrency?: TokenState;
    info?: {
      blockPerSeconds: number;
      multicallAddr?: string;
    };
    ccSwapRouter?: string;
    clusterApiUrl?: string[];
    ccSwapTokensPairs?: Partial<CCSwapTokensPairs>;
    crossChain?: {
      [key: number]: Partial<CrossChain>;
    };
    allowChains?: number[];
    account?: string;
    connector?: {
      latestProvider: StorageState<string>;
      showConnector: boolean;
    };
    walletInfo?: { visible: boolean };
    btcPubkey?: string;
  }) {
    // 基本属性
    this.name = config.name;
    this.fullName = config.fullName;
    this.chainId = config.chainId;
    this.logoUrl = config.logoUrl;
    this.rpcUrl = config.rpcUrl;
    this.explorerURL = config.explorerURL;
    this.explorerName = config.explorerName;

    // ChainState 属性
    this.alias = config.alias;
    this.isPaused = config.isPaused || false;
    this.Coin = config.Coin || new TokenState();
    this.label = config.label || "";
    this.confirmationTimes = config.confirmationTimes || 12;
    this.nativeCurrency = config.nativeCurrency || new TokenState();
    this.info = config.info || { blockPerSeconds: 15 };
    this.ccSwapRouter = config.ccSwapRouter;
    this.clusterApiUrl = config.clusterApiUrl;
    this.ccSwapTokensPairs = config.ccSwapTokensPairs || {};
    this.crossChain = config.crossChain || {};

    makeAutoObservable(this);
  }

  // 从 ChainState 继承的方法
  init() {
    if (this.Coin) {
      this.Coin.network = this;
    }
    Object.values(this.crossChain || {}).forEach((v) => {
      if (v.tokens) {
        v.tokens.forEach((token) => {
          token.network = this;
        });
      }
    });
  }

  getCrossChain(chainId: number) {
    let result = this.crossChain?.[chainId];
    if (result) return result;
    result = this.crossChain?.[4689];
    if (result) return result;
    const firstKey = Object.keys(this.crossChain)[0];
    return firstKey ? this.crossChain[parseInt(firstKey)] : undefined;
  }

  get currentChain(): ChainState {
    return this.chain.current;
  }

  // 从 NetworkState 继承的抽象方法
  abstract multicall(calls: Partial<CallParams>[]): Promise<any[]>;

  abstract loadBalance(): Promise<void>;

  abstract execContract(
    call: CallParams,
  ): Promise<Partial<TransactionResponse>>;

  abstract isAddressValid(address: string): boolean;
}

// EthNetwork 继承 Network 父类
export class EthNetwork extends Network {
  // EthNetworkState 特有的属性
  name: "PaymentMethodChangeEvent";

  constructor(config: {
    name: string;
    fullName: string;
    chainId: number;
    logoUrl: string;
    rpcUrl: string;
    explorerURL: string;
    explorerName: string;
    gasPrice?: string;
    gasLimit?: string;
    [key: string]: any;
  }) {
    super(config);

    // EthNetwork 特有的初始化
    this.connector = {
      latestProvider: new StorageState({ key: "latestEthProvider" }),
      showConnector: false,
    };
  }

  get defaultEthers() {
    // 简化实现，实际应该返回 JsonRpcProvider
    return null;
  }

  async loadBalance(): Promise<void> {
    // 空实现，实际应该加载余额
    console.log("EthNetwork loadBalance called");
  }

  readMultiContract(params: CallParams) {
    // 空实现，实际应该调用多重合约
    console.log("readMultiContract called with:", params);
    return null;
  }

  async execContract(
    params: CallParams,
  ): Promise<Partial<TransactionResponse>> {
    // 空实现，实际应该执行合约
    console.log("execContract called with:", params);
    return {};
  }

  async multicall(calls: CallParams[]): Promise<any[]> {
    // 空实现，实际应该执行多重调用
    console.log("multicall called with:", calls);
    return [];
  }

  isAddressValid(address: string): boolean {
    // 简化的地址验证，实际应该使用 ethers.utils.isAddress
    return address.startsWith("0x") && address.length === 42;
  }
}
